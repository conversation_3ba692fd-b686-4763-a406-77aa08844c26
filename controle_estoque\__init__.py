# Configuração crítica do matplotlib para evitar erro "main thread is not in main loop"
# Esta configuração deve ser feita ANTES de qualquer importação do matplotlib
import os
import sys

# Configurar backend não-interativo do matplotlib para evitar problemas de threading
os.environ['MPLBACKEND'] = 'Agg'

# Configurar matplotlib para usar backend não-interativo
try:
    import matplotlib
    matplotlib.use('Agg', force=True)

    # Configurações adicionais para estabilidade
    import matplotlib.pyplot as plt
    plt.ioff()  # Desativar modo interativo

    # Configurar para não usar GUI
    matplotlib.rcParams['backend'] = 'Agg'
    matplotlib.rcParams['interactive'] = False

except ImportError:
    # matplotlib não está instalado, continuar normalmente
    pass
except Exception as e:
    # Log do erro mas não interromper a inicialização
    print(f"Aviso: Erro ao configurar matplotlib: {e}")